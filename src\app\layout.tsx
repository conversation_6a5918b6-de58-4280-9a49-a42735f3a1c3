'use client'; // ✅ خلي الملف Client

import './globals.css';
import AnnouncementBar from '@/components/AnnouncementBar';
import TopBar from '@/components/TopBar';

import Navbar from '@/components/Navbar';
import { UserProvider } from '@/context/UserContext'; // ✅ استيراد مباشر للمزود
import 'bootstrap/dist/css/bootstrap.min.css';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <UserProvider>
          <AnnouncementBar />
          <TopBar />
          <Navbar />
          {children}
        </UserProvider>
      </body>
    </html>
  );
}
