'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FaShoppingCart } from 'react-icons/fa';
import Select from 'react-select';

const countries = [
  { name: 'Egypt', code: 'eg' },
  { name: 'Saudi Arabia', code: 'sa' },
  { name: 'UAE', code: 'ae' },
  { name: 'France', code: 'fr' },
  { name: 'Germany', code: 'de' },
  { name: 'Italy', code: 'it' },
  { name: 'Spain', code: 'es' },
  { name: 'UK', code: 'gb' },
  { name: 'Jordan', code: 'jo' },
  { name: 'Iraq', code: 'iq' },
  { name: 'Algeria', code: 'dz' },
  { name: 'Morocco', code: 'ma' },
];

const countryOptions = countries.map((c) => ({
  value: c.code,
  label: c.name,
  flag: `https://flagcdn.com/w40/${c.code.toLowerCase()}.png`,
}));

export default function TopBar() {
  const [mounted, setMounted] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(countryOptions[0]);
  const [showCart, setShowCart] = useState(false);

  useEffect(() => setMounted(true), []);
  if (!mounted) return null;

  return (
    <div className="relative w-full bg-white shadow-sm">
      <div className="mx-auto px-2 py-3 flex flex-nowrap items-center justify-between gap-2 overflow-hidden min-w-fit">

        {/* Logo */}
        <div className="flex-shrink-0 min-w-fit">
          <Image src="/logo.png" alt="Logo" width={40} height={40} />
        </div>

        {/* Search */}
        <div className="flex-shrink-0 min-w-[150px] max-w-[200px] px-2">
          <input
            type="text"
            placeholder="Search..."
            className="w-full px-3 py-2 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 text-xs"
          />
        </div>

        {/* Controls */}
        <div className="flex items-center gap-2 flex-nowrap flex-shrink-0 min-w-fit">
          
          {/* Country Selector */}
          <div className="w-[100px] flex-shrink-0 min-w-fit">
            <Select
              options={countryOptions}
              value={selectedCountry}
              onChange={(val) => setSelectedCountry(val)}
              isSearchable={false}
              className="text-xs"
              formatOptionLabel={(option) => (
                <div className="flex items-center gap-1">
                  <img
                    src={option.flag}
                    alt={option.label}
                    style={{
                      width: '16px',
                      height: '12px',
                      objectFit: 'cover',
                      borderRadius: '2px',
                    }}
                  />
                  <span className="text-xs truncate">{option.label}</span>
                </div>
              )}
              styles={{
                control: (base) => ({
                  ...base,
                  borderRadius: '999px',
                  paddingLeft: '4px',
                  minHeight: '32px',
                  boxShadow: 'none',
                  border: 'none',
                  minWidth: '100px',
                }),
                indicatorsContainer: (base) => ({
                  ...base,
                  paddingRight: '4px',
                }),
                valueContainer: (base) => ({
                  ...base,
                  padding: '0 4px',
                }),
              }}
            />
          </div>

          {/* Cart Icon */}
          <button
            onClick={() => setShowCart(!showCart)}
            className="text-gray-500 hover:text-blue-600 text-lg relative bg-transparent border-none outline-none flex-shrink-0 min-w-fit"
          >
            <FaShoppingCart />
          </button>

          {/* Mini Cart Dropdown */}
          {showCart && (
            <div className="absolute top-[50px] right-0 bg-white shadow-lg rounded-lg w-[300px] z-50">
              <div className="p-4">
                <h3 className="text-sm font-semibold mb-2">Shopping Cart</h3>
                <div className="border-t pt-2">
                  <div className="flex justify-between items-center mb-2">
                    <div>
                      <p className="text-sm font-medium">Sample Product</p>
                      <p className="text-xs text-gray-500">Qty: 1</p>
                    </div>
                    <span className="text-sm">$10</span>
                  </div>
                </div>
                <Link
                  href="/cart"
                  className="block text-center mt-4 bg-blue-500 text-white py-2 rounded hover:bg-blue-600 transition"
                >
                  Go to Cart
                </Link>
              </div>
            </div>
          )}

          {/* Login Button */}
     <Link
  href="/login"
  className="inline-block bg-blue-500 text-white text-xs font-medium px-4 py-2 rounded-full shadow hover:bg-blue-600 transition duration-300 flex-shrink-0 min-w-fit whitespace-nowrap no-underline"
>
  Login
</Link>

        </div>
      </div>
    </div>
  );
}
