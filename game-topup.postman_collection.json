{"info": {"_postman_id": "aa0478f9-99f6-41e5-b1b5-eb5ce008896f", "name": "game-topup", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "27864674"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "signUp", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{  \"userName\":\"esraa\"  ,\r\n  \"email\":\"<EMAIL>\",\r\n    \"password\":\"123456789\",\r\n    \"confirmPassword\":\"123456789\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/auth/", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["auth", ""]}}, "response": []}, {"name": "login", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.i-xJGFPHYCPx1cJPPmpjFx-43qAPsMGHTf-pKJdgAiY", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{   \"email\":\"<EMAIL>\",\r\n    \"password\":\"123456789\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["auth", "login"]}}, "response": []}, {"name": "forget", "request": {"method": "POST", "header": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************.9MbB0u47CStboNwOwdXIbsKf0jJymA7Qx5BHwFpNLqU", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{ \r\n  \"email\":\"<EMAIL>\"\r\n  \r\n   \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/auth/forget", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["auth", "forget"]}}, "response": []}, {"name": "resetPassword", "request": {"method": "POST", "header": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************.9MbB0u47CStboNwOwdXIbsKf0jJymA7Qx5BHwFpNLqU", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \r\n    \r\n      \"forgetCode\":\"9468\",\r\n     \"newPassword\":\"12345678\",\r\n     \"confirmpassword\":\"12345678\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/auth/reset", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["auth", "reset"]}}, "response": []}]}, {"name": "user", "item": [{"name": "changePassword", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.i-xJGFPHYCPx1cJPPmpjFx-43qAPsMGHTf-pKJdgAiY", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{      \"oldPassword\":\"1455\",\r\n    \"newPassword\":\"11455\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/user/changePassword/66215f64baa3d82a97050c7f", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["user", "changePassword", "66215f64baa3d82a97050c7f"]}}, "response": []}, {"name": "logout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.i-xJGFPHYCPx1cJPPmpjFx-43qAPsMGHTf-pKJdgAiY", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userId\": \"6618d1a87856cd6fdf1f84c2\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/user/logout", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["user", "logout"]}}, "response": []}, {"name": "editProfile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.i-xJGFPHYCPx1cJPPmpjFx-43qAPsMGHTf-pKJdgAiY", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\"userName\":\"esraa\",\r\n\"email\":\"<EMAIL>\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/user/editProfile/66215f64baa3d82a97050c7f", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["user", "editProfile", "66215f64baa3d82a97050c7f"]}}, "response": []}, {"name": "updateprofilepic", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.i-xJGFPHYCPx1cJPPmpjFx-43qAPsMGHTf-pKJdgAiY", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "image", "type": "file", "src": "/C:/Users/<USER>/OneDrive/Pictures/11.jpg"}, {"key": "", "value": "", "type": "text", "disabled": true}, {"key": "", "type": "file", "src": [], "disabled": true}]}, "url": {"raw": "http://localhost:3000/user/updateprofilepic/66215f64baa3d82a97050c7f", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["user", "updateprofilepic", "66215f64baa3d82a97050c7f"]}}, "response": []}, {"name": "profilepic", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.i-xJGFPHYCPx1cJPPmpjFx-43qAPsMGHTf-pKJdgAiY", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "image", "type": "file", "src": "postman-cloud:///1eed8ea5-dd91-4a00-95b5-31d7a9a56370"}]}, "url": {"raw": "http://localhost:3000/user/profilepic/66215f64baa3d82a97050c7f", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["user", "profilepic", "66215f64baa3d82a97050c7f"]}}, "response": []}, {"name": "delete", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/user/deleteUser/66215f64baa3d82a97050c7f", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["user", "deleteUser", "66215f64baa3d82a97050c7f"]}}, "response": []}, {"name": "getuserById", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/user/getuserById/66215f64baa3d82a97050c7f", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["user", "getuserById", "66215f64baa3d82a97050c7f"]}}, "response": []}]}, {"name": "order", "item": [{"name": "getorder", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "e_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4M2ZlZWZkYWVhYzQ5MzNhZmY5NDFlNiIsInJvbGUiOiJVc2VyIiwiaWF0IjoxNzQ5ODcwMDI1LCJleHAiOjE3NTI0NjIwMjV9.U8WaNZ7o9QUTs2_R0BxrfgSOjDMj7YqIb5WlPmVVveM", "type": "text"}], "url": {"raw": "http://localhost:3000/order", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["order"]}}, "response": []}, {"name": "createOrder", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "e_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NGZkNGIzZjU0MGI1ZmFkNDBlNzdhMCIsInJvbGUiOiJVc2VyIiwiaWF0IjoxNzUwMDYyMzI3LCJleHAiOjE3NTI2NTQzMjd9.MXdeSZgg8YbW1A92QCamLutnDCGHml_CZeaW67x4JZk", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"type\": \"top-up\",\r\n  \"game\": \"PUBG\",\r\n    \"productDetails\": {\r\n    \"gamerId\": \"123456789\"\r\n  },\r\n  \"amount\": 5,\r\n  \"paymentMethod\": \"wallet\" ,\r\n   \"packageType\": \"60uc\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/order", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["order"]}}, "response": []}]}, {"name": "payment", "item": [{"name": "createPaypal", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "e_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4M2ZlZWZkYWVhYzQ5MzNhZmY5NDFlNiIsInJvbGUiOiJVc2VyIiwiaWF0IjoxNzQ5ODcwMDI1LCJleHAiOjE3NTI0NjIwMjV9.U8WaNZ7o9QUTs2_R0BxrfgSOjDMj7YqIb5WlPmVVveM", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"amount\": 10\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/paypal/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["paypal", "create"]}}, "response": []}, {"name": "createOrder", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "e_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4M2ZlZWZkYWVhYzQ5MzNhZmY5NDFlNiIsInJvbGUiOiJVc2VyIiwiaWF0IjoxNzQ5ODcwMDI1LCJleHAiOjE3NTI0NjIwMjV9.U8WaNZ7o9QUTs2_R0BxrfgSOjDMj7YqIb5WlPmVVveM", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"orderId\": \"646614872L495443R\"\r\n}\r\n\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/paypal/capture", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["paypal", "capture"]}}, "response": []}]}, {"name": "wallet", "item": [{"name": "rechargerequest", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "e_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NGZkNGIzZjU0MGI1ZmFkNDBlNzdhMCIsInJvbGUiOiJVc2VyIiwiaWF0IjoxNzUwMDYyMzI3LCJleHAiOjE3NTI2NTQzMjd9.MXdeSZgg8YbW1A92QCamLutnDCGHml_CZeaW67x4JZk", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"country\": \"EG\",\r\n  \"telecom\": \"Vodafone\",\r\n  \"amount\": 100,\r\n  \"phoneNumber\":\"01052948236\"\r\n\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/wallet/rechargerequest", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["wallet", "rechargerequest"]}}, "response": []}, {"name": "balancewallet", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "e_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4M2ZlZWZkYWVhYzQ5MzNhZmY5NDFlNiIsInJvbGUiOiJVc2VyIiwiaWF0IjoxNzQ5ODc2NTA0LCJleHAiOjE3NTI0Njg1MDR9.7WHL3iS-IW3qw7FecJa5clCidiGa8RatseUz6HOorBU", "type": "text"}], "url": {"raw": "http://localhost:3000/wallet/balance", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["wallet", "balance"]}}, "response": []}, {"name": "transactions", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "e_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NGZkNGIzZjU0MGI1ZmFkNDBlNzdhMCIsInJvbGUiOiJVc2VyIiwiaWF0IjoxNzUwMDYyMzI3LCJleHAiOjE3NTI2NTQzMjd9.MXdeSZgg8YbW1A92QCamLutnDCGHml_CZeaW67x4JZk", "type": "text"}], "url": {"raw": "http://localhost:3000/wallet/transactions", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["wallet", "transactions"]}}, "response": []}]}, {"name": "simcards", "item": [{"name": "recharge card", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/simcards", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["simcards"]}}, "response": []}]}]}