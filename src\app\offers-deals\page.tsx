export default function OffersDealsPage() {
  return (
    <main className="p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Offers & Deals</h1>
        <p className="text-gray-600 mb-8">Don't miss out on our amazing limited-time offers and exclusive deals!</p>
        
        {/* Flash Deals */}
        <div className="bg-gradient-to-r from-red-500 to-pink-500 text-white p-6 rounded-lg mb-8">
          <h2 className="text-2xl font-bold mb-4">⚡ Flash Deals - Limited Time!</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white bg-opacity-20 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Gaming Accounts</h3>
              <p className="text-sm mb-2">Up to 70% OFF</p>
              <p className="text-xs">Ends in: 2h 45m</p>
            </div>
            <div className="bg-white bg-opacity-20 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Social Media Bundle</h3>
              <p className="text-sm mb-2">Buy 2 Get 1 FREE</p>
              <p className="text-xs">Ends in: 5h 12m</p>
            </div>
            <div className="bg-white bg-opacity-20 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Top-Up Credits</h3>
              <p className="text-sm mb-2">Extra 25% Bonus</p>
              <p className="text-xs">Ends in: 1h 30m</p>
            </div>
          </div>
        </div>
        
        {/* Weekly Deals */}
        <h2 className="text-2xl font-semibold mb-6">🔥 Weekly Deals</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md overflow-hidden border-2 border-green-500">
            <div className="bg-green-500 text-white p-2 text-center font-semibold">
              50% OFF
            </div>
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-3">Premium Streaming Package</h3>
              <p className="text-gray-600 mb-4">Netflix, Spotify, Disney+ and more</p>
              <div className="flex justify-between items-center mb-4">
                <div>
                  <span className="text-gray-500 line-through">$100</span>
                  <span className="text-2xl font-bold text-green-600 ml-2">$50</span>
                </div>
              </div>
              <button className="w-full bg-green-500 text-white py-2 rounded hover:bg-green-600">
                Grab Deal
              </button>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden border-2 border-blue-500">
            <div className="bg-blue-500 text-white p-2 text-center font-semibold">
              BUY 1 GET 1
            </div>
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-3">Social Media Accounts</h3>
              <p className="text-gray-600 mb-4">Instagram and Facebook verified accounts</p>
              <div className="flex justify-between items-center mb-4">
                <div>
                  <span className="text-2xl font-bold text-blue-600">$75</span>
                  <span className="text-sm text-gray-500 ml-1">for 2</span>
                </div>
              </div>
              <button className="w-full bg-blue-500 text-white py-2 rounded hover:bg-blue-600">
                Get Offer
              </button>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden border-2 border-purple-500">
            <div className="bg-purple-500 text-white p-2 text-center font-semibold">
              BONUS 30%
            </div>
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-3">Gaming Credits</h3>
              <p className="text-gray-600 mb-4">Extra 30% credits on all top-ups</p>
              <div className="flex justify-between items-center mb-4">
                <div>
                  <span className="text-2xl font-bold text-purple-600">+30%</span>
                  <span className="text-sm text-gray-500 ml-1">bonus</span>
                </div>
              </div>
              <button className="w-full bg-purple-500 text-white py-2 rounded hover:bg-purple-600">
                Top Up Now
              </button>
            </div>
          </div>
        </div>
        
        {/* Seasonal Offers */}
        <h2 className="text-2xl font-semibold mb-6">🎉 Seasonal Offers</h2>
        <div className="bg-yellow-50 border border-yellow-200 p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-semibold text-yellow-800 mb-2">Summer Sale Event</h3>
              <p className="text-yellow-700">Massive discounts on all categories. Limited time only!</p>
            </div>
            <button className="bg-yellow-500 text-white px-6 py-3 rounded-lg hover:bg-yellow-600 font-semibold">
              Shop Now
            </button>
          </div>
        </div>
      </div>
    </main>
  );
}
