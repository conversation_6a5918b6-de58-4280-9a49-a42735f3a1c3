"use client";

import { useState } from "react";
import { useUser } from "../../context/UserContext";
import { useRouter } from "next/navigation";
import Link from "next/link";

export default function Signup() {
  const { setUser } = useUser();
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [name, setName] = useState("");
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password || !confirmPassword || !name) {
      setError("Please fill all fields");
      return;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    try {
      const res = await fetch("http://localhost:3000/auth/", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userName: name,
          email,
          password,
          confirmPassword,
        }),
      });

      // تحقق من نوع المحتوى قبل محاولة parse JSON
      const contentType = res.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        const data = await res.json();

        if (res.ok && data.token) {
          // حفظ المستخدم في localStorage
          localStorage.setItem("username", name);
          localStorage.setItem("token", data.token);

          // تحديث السياق
          setUser({
            email,
            name,
            photoURL: "/avatar.svg",
          });

          router.push("/");
          return;
        } else {
          setError(data.message || "Registration failed");
          return;
        }
      }

      // إذا لم يكن JSON، استخدم النظام الوهمي
      const mockToken = "mock_token_" + Date.now();

      localStorage.setItem("username", name);
      localStorage.setItem("token", mockToken);

      setUser({
        email,
        name,
        photoURL: "/avatar.svg",
      });

      router.push("/");
    } catch (err) {
      console.error("Signup error:", err);
      // استخدم إنشاء حساب وهمي عند فشل الـ API
      const mockToken = "mock_token_" + Date.now();

      localStorage.setItem("username", name);
      localStorage.setItem("token", mockToken);

      setUser({
        email,
        name,
        photoURL: "/avatar.svg",
      });

      router.push("/");
    }
  };

  return (
    <div className="max-w-md mx-auto mt-20 p-6 bg-white rounded shadow">
      <h1 className="text-2xl mb-6 font-semibold text-center">Create Account</h1>

      {error && <p className="text-red-500 mb-4">{error}</p>}

      <form onSubmit={handleSignup} className="space-y-4">
        <input
          type="text"
          placeholder="Full Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          className="w-full border px-3 py-2 rounded"
          suppressHydrationWarning
        />

        <input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full border px-3 py-2 rounded"
          suppressHydrationWarning
        />

        <div className="relative">
          <input
            type={showPassword ? "text" : "password"}
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full border px-3 py-2 rounded"
            suppressHydrationWarning
          />
          <span
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-2.5 cursor-pointer text-sm text-gray-500"
          >
            {showPassword ? "Hide" : "Show"}
          </span>
        </div>

        <div className="relative">
          <input
            type={showConfirmPassword ? "text" : "password"}
            placeholder="Confirm Password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            className="w-full border px-3 py-2 rounded"
            suppressHydrationWarning
          />
          <span
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute right-3 top-2.5 cursor-pointer text-sm text-gray-500"
          >
            {showConfirmPassword ? "Hide" : "Show"}
          </span>
        </div>

        <button
          type="submit"
          className=" animate-bounce w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded font-semibold"
          suppressHydrationWarning
        >
          Create Account
        </button>
      </form>

      <p className="mt-6 text-center text-sm text-gray-600">
        Already have an account?{" "}
        <Link href="/login" className="text-blue-500 hover:underline">
          Login
        </Link>
      </p>
    </div>
  );
}
