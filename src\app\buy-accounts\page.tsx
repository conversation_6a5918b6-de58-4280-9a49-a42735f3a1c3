export default function BuyAccountsPage() {
  return (
    <main className="p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Buy Accounts</h1>
        <p className="text-gray-600 mb-8">Browse and purchase verified accounts from trusted sellers.</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-3">Social Media Accounts</h3>
            <p className="text-gray-600 mb-4">Instagram, Facebook, Twitter accounts</p>
            <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
              View Accounts
            </button>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-3">Gaming Accounts</h3>
            <p className="text-gray-600 mb-4">Steam, PlayStation, Xbox accounts</p>
            <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
              View Accounts
            </button>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-3">Streaming Accounts</h3>
            <p className="text-gray-600 mb-4">Netflix, Spotify, YouTube Premium</p>
            <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
              View Accounts
            </button>
          </div>
        </div>
      </div>
    </main>
  );
}
