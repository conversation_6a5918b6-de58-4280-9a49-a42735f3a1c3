'use client';

import Link from 'next/link';
import Container from 'react-bootstrap/Container';
import Nav from 'react-bootstrap/Nav';
import Navbar from 'react-bootstrap/Navbar';
import 'bootstrap/dist/css/bootstrap.min.css';
import './NavbarStyles.css'; // ملف ستايل اختياري لو عندك تخصيص إضافي

export default function ResponsiveNavbar() {
  return (
    <Navbar expand="lg" bg="white" className="shadow-sm px-3">
      <Container fluid>
        {/* اللوجو أو اسم الموقع */}
        <Navbar.Brand className="fw-bold text-primary">🛒 Buy1</Navbar.Brand>

        {/* زر القائمة عند تصغير الشاشة */}
        <Navbar.Toggle
          aria-controls="basic-navbar-nav"
          className="ms-auto border-0 shadow-none custom-toggle"
        />

        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="mx-auto">
            <Link href="/" className="nav-link mx-3 nav-link-custom">
              Home
            </Link>

            <Link href="/buy-accounts" className="nav-link mx-3 nav-link-custom">
              Buy Accounts
            </Link>

            <Link href="/sell-accounts" className="nav-link mx-3 nav-link-custom">
              Sell Accounts
            </Link>

            <Link href="/top-up" className="nav-link mx-3 nav-link-custom">
              Top-Up
            </Link>

            <Link href="/internet-recharge" className="nav-link mx-3 nav-link-custom">
              Internet Recharge
            </Link>

            <Link href="/marketplace" className="nav-link mx-3 nav-link-custom">
              Marketplace
            </Link>

            <Link href="/offers-deals" className="nav-link mx-3 nav-link-custom">
              Offers & Deals
            </Link>
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
}
