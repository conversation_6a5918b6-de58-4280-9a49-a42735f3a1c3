'use client';

import { useState } from 'react';
import Link from 'next/link';

const menuItems = [
  { name: 'Buy Accounts', href: '/buy-accounts' },
  { name: 'Sell Accounts', href: '/sell-accounts' },
  { name: 'Top-Up', href: '/top-up' },
  { name: 'Internet Recharge', href: '/internet-recharge' },
  { name: 'Marketplace', href: '/marketplace' },
  { name: 'Offers & Deals', href: '/offers-deals' },
];

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="bg-white shadow-sm border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4">

        {/* Desktop Navigation - في نص الشاشة */}
        <div className="hidden md:flex items-center justify-center py-3">
          <div className="flex items-center space-x-6">
            {menuItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-black hover:text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>

        {/* Mobile Navigation - زر الـ 3 شرط فقط في الهاتف */}
        <div className="md:hidden">
          {/* Mobile Menu Button - في نص الشاشة */}
          <div className="flex items-center justify-center py-3">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-black hover:text-blue-600 focus:outline-none p-2"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>

          {/* Mobile Menu Items - 3 rows */}
          {isMenuOpen && (
            <div className="pb-4">
              <div className="space-y-2">
                {/* Row 1: Home, Buy Accounts, Sell Accounts */}
                <div className="flex justify-center space-x-2">
                  <Link
                    href="/"
                    className="text-black hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md text-sm font-medium text-center transition-colors duration-200 flex-1"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Home
                  </Link>
                  <Link
                    href="/buy-accounts"
                    className="text-black hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md text-sm font-medium text-center transition-colors duration-200 flex-1"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Buy Accounts
                  </Link>
                  <Link
                    href="/sell-accounts"
                    className="text-black hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md text-sm font-medium text-center transition-colors duration-200 flex-1"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sell Accounts
                  </Link>
                </div>

                {/* Row 2: Top-Up, Internet Recharge */}
                <div className="flex justify-center space-x-2">
                  <Link
                    href="/top-up"
                    className="text-black hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md text-sm font-medium text-center transition-colors duration-200 flex-1"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Top-Up
                  </Link>
                  <Link
                    href="/internet-recharge"
                    className="text-black hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md text-sm font-medium text-center transition-colors duration-200 flex-1"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Internet Recharge
                  </Link>
                </div>

                {/* Row 3: Marketplace, Offers & Deals */}
                <div className="flex justify-center space-x-2">
                  <Link
                    href="/marketplace"
                    className="text-black hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md text-sm font-medium text-center transition-colors duration-200 flex-1"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Marketplace
                  </Link>
                  <Link
                    href="/offers-deals"
                    className="text-black hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-md text-sm font-medium text-center transition-colors duration-200 flex-1"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Offers & Deals
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}