'use client';

import Link from 'next/link';
import Container from 'react-bootstrap/Container';
import Nav from 'react-bootstrap/Nav';
import Navbar from 'react-bootstrap/Navbar';
import 'bootstrap/dist/css/bootstrap.min.css';
import './NavbarStyles.css'; // ملف ستايل اختياري لو عندك تخصيص إضافي

export default function ResponsiveNavbar() {
  return (
    <Navbar expand="lg" bg="white" className="shadow-sm px-3">
      <Container fluid>
        {/* اللوجو أو اسم الموقع */}
        <Navbar.Brand className="fw-bold text-primary">🛒 Buy1</Navbar.Brand>

        {/* زر القائمة عند تصغير الشاشة */}
        <Navbar.Toggle
          aria-controls="basic-navbar-nav"
          className="ms-auto border-0 shadow-none custom-toggle"
        />

        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="mx-auto">
            <Nav.Link as={Link} href="/" className="mx-3 nav-link-custom">
              Home
            </Nav.Link>

            <Nav.Link as={Link} href="/buy-accounts" className="mx-3 nav-link-custom">
              Buy Accounts
            </Nav.Link>

            <Nav.Link as={Link} href="/sell-accounts" className="mx-3 nav-link-custom">
              Sell Accounts
            </Nav.Link>

            <Nav.Link as={Link} href="/top-up" className="mx-3 nav-link-custom">
              Top-Up
            </Nav.Link>

            <Nav.Link as={Link} href="/internet-recharge" className="mx-3 nav-link-custom">
              Internet Recharge
            </Nav.Link>

            <Nav.Link as={Link} href="/marketplace" className="mx-3 nav-link-custom">
              Marketplace
            </Nav.Link>

            <Nav.Link as={Link} href="/offers-deals" className="mx-3 nav-link-custom">
              Offers & Deals
            </Nav.Link>
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
}
