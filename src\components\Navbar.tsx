import Container from 'react-bootstrap/Container';
import Nav from 'react-bootstrap/Nav';
import Navbar from 'react-bootstrap/Navbar';
import 'bootstrap/dist/css/bootstrap.min.css';
import './NavbarStyles.css'; // هنعمل ملف CSS لتعديلات بسيطة

function ResponsiveNavbar() {
  return (
    <>
      <Navbar expand="lg" bg="white" className="shadow-sm px-3">
        <Container fluid>
          {/* زر التلت شرط في اليمين */}
          <Navbar.Toggle
            aria-controls="basic-navbar-nav"
            className="ms-auto border-0 shadow-none custom-toggle"
          />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="mx-auto">
              <Nav.Link href="#home" className="mx-3 nav-link-custom">Home</Nav.Link>
              <Nav.Link href="#buy-accounts" className="mx-3 nav-link-custom">Buy Accounts</Nav.Link>
              <Nav.Link href="#sell-accounts" className="mx-3 nav-link-custom">Sell Accounts</Nav.Link>
              <Nav.Link href="#top-up" className="mx-3 nav-link-custom">Top-Up</Nav.Link>
              <Nav.Link href="#internet-recharge" className="mx-3 nav-link-custom">Internet Recharge</Nav.Link>
              <Nav.Link href="#marketplace" className="mx-3 nav-link-custom">Marketplace</Nav.Link>
              <Nav.Link href="#offers-deals" className="mx-3 nav-link-custom">Offers & Deals</Nav.Link>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>
    </>
  );
}

export default ResponsiveNavbar;
