'use client';

import Link from 'next/link';
import { useState } from 'react';
import { Menu } from 'lucide-react'; // أيقونة الثلاث شرط (ممكن تستبدلها بـ SVG لو حبيت)

export default function Navbar() {
  const [menuOpen, setMenuOpen] = useState(false);

  return (
    <nav className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between">
        <div className="text-xl font-bold text-blue-600">Logo</div>

        {/* زر الهامبرجر يظهر في الشاشات الصغيرة */}
        <div className="md:hidden">
          <button onClick={() => setMenuOpen(!menuOpen)}>
            <Menu className="w-6 h-6 text-black" />
          </button>
        </div>

        {/* روابط الناف بار: مخفية في الموبايل وتظهر في الشاشات الكبيرة */}
        <div className="hidden md:flex space-x-6">
          <Link href="/" className="text-black hover:text-blue-600 font-medium">Home</Link>
          <Link href="/about" className="text-black hover:text-blue-600 font-medium">About</Link>
          <Link href="/services" className="text-black hover:text-blue-600 font-medium">Services</Link>
          <Link href="/contact" className="text-black hover:text-blue-600 font-medium">Contact</Link>
        </div>
      </div>

      {/* القائمة تظهر فقط في الموبايل عند الضغط على الزر */}
      {menuOpen && (
        <div className="md:hidden px-4 pb-4 space-y-2">
          <Link href="/" className="block text-black hover:text-blue-600 font-medium">Home</Link>
          <Link href="/about" className="block text-black hover:text-blue-600 font-medium">About</Link>
          <Link href="/services" className="block text-black hover:text-blue-600 font-medium">Services</Link>
          <Link href="/contact" className="block text-black hover:text-blue-600 font-medium">Contact</Link>
        </div>
      )}
    </nav>
  );
}
