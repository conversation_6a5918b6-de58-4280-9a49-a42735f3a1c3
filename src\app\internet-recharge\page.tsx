export default function InternetRechargePage() {
  return (
    <main className="p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Internet Recharge</h1>
        <p className="text-gray-600 mb-8">Recharge your internet data packages and broadband services.</p>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Mobile Data Packages */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-semibold mb-6">Mobile Data Packages</h2>
            
            <div className="space-y-4">
              <div className="border border-gray-200 p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-semibold">1GB Data</h3>
                    <p className="text-gray-600">Valid for 30 days</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xl font-bold text-blue-600">$5</p>
                    <button className="bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600">
                      Buy Now
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="border border-gray-200 p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-semibold">5GB Data</h3>
                    <p className="text-gray-600">Valid for 30 days</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xl font-bold text-blue-600">$15</p>
                    <button className="bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600">
                      Buy Now
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="border border-gray-200 p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-semibold">10GB Data</h3>
                    <p className="text-gray-600">Valid for 30 days</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xl font-bold text-blue-600">$25</p>
                    <button className="bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600">
                      Buy Now
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Broadband Services */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-semibold mb-6">Broadband Services</h2>
            
            <div className="space-y-4">
              <div className="border border-gray-200 p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-semibold">Basic Plan</h3>
                    <p className="text-gray-600">50 Mbps - Monthly</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xl font-bold text-green-600">$30</p>
                    <button className="bg-green-500 text-white px-4 py-2 rounded text-sm hover:bg-green-600">
                      Subscribe
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="border border-gray-200 p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-semibold">Premium Plan</h3>
                    <p className="text-gray-600">100 Mbps - Monthly</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xl font-bold text-green-600">$50</p>
                    <button className="bg-green-500 text-white px-4 py-2 rounded text-sm hover:bg-green-600">
                      Subscribe
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="border border-gray-200 p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-semibold">Ultra Plan</h3>
                    <p className="text-gray-600">200 Mbps - Monthly</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xl font-bold text-green-600">$80</p>
                    <button className="bg-green-500 text-white px-4 py-2 rounded text-sm hover:bg-green-600">
                      Subscribe
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
