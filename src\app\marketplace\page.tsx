export default function MarketplacePage() {
  return (
    <main className="p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Marketplace</h1>
        <p className="text-gray-600 mb-8">Discover amazing products and services from our community.</p>
        
        {/* Categories */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
          <div className="bg-white p-4 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow cursor-pointer">
            <div className="text-3xl mb-2">📱</div>
            <p className="text-sm font-medium">Electronics</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow cursor-pointer">
            <div className="text-3xl mb-2">👕</div>
            <p className="text-sm font-medium">Fashion</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow cursor-pointer">
            <div className="text-3xl mb-2">🏠</div>
            <p className="text-sm font-medium">Home</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow cursor-pointer">
            <div className="text-3xl mb-2">🎮</div>
            <p className="text-sm font-medium">Gaming</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow cursor-pointer">
            <div className="text-3xl mb-2">📚</div>
            <p className="text-sm font-medium">Books</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow cursor-pointer">
            <div className="text-3xl mb-2">🚗</div>
            <p className="text-sm font-medium">Automotive</p>
          </div>
        </div>
        
        {/* Featured Products */}
        <h2 className="text-2xl font-semibold mb-6">Featured Products</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="h-48 bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500">Product Image</span>
            </div>
            <div className="p-4">
              <h3 className="font-semibold mb-2">Premium Gaming Account</h3>
              <p className="text-gray-600 text-sm mb-3">High-level gaming account with rare items</p>
              <div className="flex justify-between items-center">
                <span className="text-xl font-bold text-blue-600">$150</span>
                <button className="bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600">
                  Buy Now
                </button>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="h-48 bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500">Product Image</span>
            </div>
            <div className="p-4">
              <h3 className="font-semibold mb-2">Social Media Bundle</h3>
              <p className="text-gray-600 text-sm mb-3">Multiple verified social media accounts</p>
              <div className="flex justify-between items-center">
                <span className="text-xl font-bold text-blue-600">$89</span>
                <button className="bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600">
                  Buy Now
                </button>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="h-48 bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500">Product Image</span>
            </div>
            <div className="p-4">
              <h3 className="font-semibold mb-2">Streaming Package</h3>
              <p className="text-gray-600 text-sm mb-3">Netflix, Spotify, and more accounts</p>
              <div className="flex justify-between items-center">
                <span className="text-xl font-bold text-blue-600">$45</span>
                <button className="bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600">
                  Buy Now
                </button>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="h-48 bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500">Product Image</span>
            </div>
            <div className="p-4">
              <h3 className="font-semibold mb-2">Digital Gift Cards</h3>
              <p className="text-gray-600 text-sm mb-3">Various denominations available</p>
              <div className="flex justify-between items-center">
                <span className="text-xl font-bold text-blue-600">$25</span>
                <button className="bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600">
                  Buy Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
