/* NavbarStyles.css */

/* تنسيق زر الـ 3 شرط */
.custom-toggle {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.custom-toggle:focus {
  box-shadow: none !important;
  border: none !important;
}

.custom-toggle .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

.custom-toggle:hover .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2837, 99, 235, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* تنسيق روابط الناف بار */
.nav-link-custom {
  color: #000000 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  text-decoration: none !important;
  display: inline-block;
}

.nav-link-custom:hover {
  color: #2563eb !important;
  background-color: #eff6ff !important;
  text-decoration: none !important;
}

.nav-link-custom:focus {
  color: #2563eb !important;
  background-color: #eff6ff !important;
  text-decoration: none !important;
}

/* إضافة تنسيق للـ nav-link class */
.nav-link {
  text-decoration: none !important;
}

/* تنسيق الناف بار العام */
.navbar {
  background-color: #ffffff !important;
  border-bottom: 1px solid #e5e7eb;
}

/* تنسيق responsive للموبايل */
@media (max-width: 991.98px) {
  .navbar-collapse {
    text-align: center;
    margin-top: 1rem;
  }
  
  .nav-link-custom {
    margin: 0.25rem 0 !important;
    display: block;
  }
}
